CREATE EXTENSION IF NOT EXISTS "ltree";

CREATE TABLE Topics (
    topic_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    topic_name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    parent_topic_id UUID REFERENCES Topics(topic_id),
    path LTREE NOT NULL,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now()
);

CREATE INDEX topics_path_gist_idx ON Topics USING GIST (path);
CREATE INDEX topics_parent_idx ON Topics (parent_topic_id);
