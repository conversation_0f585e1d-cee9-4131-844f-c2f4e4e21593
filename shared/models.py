from typing import Optional, List
from uuid import UUID
from datetime import datetime
from pydantic import BaseModel, EmailStr, Field
from sqlalchemy import (
    Column, String, Integer, <PERSON>olean, Float, TIMESTAMP, JSON, Foreign<PERSON>ey, UniqueConstraint, BigInteger
)
from sqlalchemy.dialects.postgresql import UUID as PG_UUID, JSONB
from sqlalchemy_utils.types.ltree import LtreeType
from sqlalchemy.orm import declarative_base, relationship, Mapped, mapped_column

Base = declarative_base()

# --- Pydantic Schemas ---

class UserCreate(BaseModel):
    email: EmailStr
    password: str

class UserResponse(BaseModel):
    user_id: UUID
    email: EmailStr
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class ItemCreate(BaseModel):
    item_type: str
    content: dict
    author_id: Optional[UUID]
    status: Optional[str] = "pretest"
    version: Optional[int] = 1

class ItemResponse(BaseModel):
    item_id: UUID
    item_type: str
    content: dict
    author_id: Optional[UUID]
    status: str
    version: int
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class TopicResponse(BaseModel):
    topic_id: UUID
    topic_name: str
    slug: str
    parent_topic_id: Optional[UUID]
    path: str
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

# --- SQLAlchemy ORM Models ---

class User(Base):
    __tablename__ = "users"
    user_id: Mapped[UUID] = mapped_column(PG_UUID(as_uuid=True), primary_key=True)
    email: Mapped[str] = mapped_column(String(255), unique=True, nullable=False)
    hashed_password: Mapped[str] = mapped_column(String(255), nullable=False)
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP(timezone=True), nullable=False)
    updated_at: Mapped[datetime] = mapped_column(TIMESTAMP(timezone=True), nullable=False)

class Item(Base):
    __tablename__ = "items"
    item_id: Mapped[UUID] = mapped_column(PG_UUID(as_uuid=True), primary_key=True)
    item_type: Mapped[str] = mapped_column(String(50), nullable=False)
    content: Mapped[dict] = mapped_column(JSONB, nullable=False)
    author_id: Mapped[Optional[UUID]] = mapped_column(PG_UUID(as_uuid=True), ForeignKey("users.user_id"))
    status: Mapped[str] = mapped_column(String(50), nullable=False, default="pretest")
    version: Mapped[int] = mapped_column(Integer, nullable=False, default=1)
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP(timezone=True), nullable=False)
    updated_at: Mapped[datetime] = mapped_column(TIMESTAMP(timezone=True), nullable=False)

class Topic(Base):
    __tablename__ = "topics"
    topic_id: Mapped[UUID] = mapped_column(PG_UUID(as_uuid=True), primary_key=True)
    topic_name: Mapped[str] = mapped_column(String(255), nullable=False)
    slug: Mapped[str] = mapped_column(String(255), unique=True, nullable=False)
    parent_topic_id: Mapped[Optional[UUID]] = mapped_column(PG_UUID(as_uuid=True), ForeignKey("topics.topic_id"))
    path: Mapped[str] = mapped_column(LtreeType(), nullable=False)
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP(timezone=True), nullable=False)
    updated_at: Mapped[datetime] = mapped_column(TIMESTAMP(timezone=True), nullable=False)

class CognitiveQMatrix(Base):
    __tablename__ = "cognitiveqmatrix"
    cognitive_q_matrix_id: Mapped[UUID] = mapped_column(PG_UUID(as_uuid=True), primary_key=True)
    item_id: Mapped[UUID] = mapped_column(PG_UUID(as_uuid=True), ForeignKey("items.item_id"))
    topic_id: Mapped[UUID] = mapped_column(PG_UUID(as_uuid=True), ForeignKey("topics.topic_id"))
    bloom_dimension_id: Mapped[int] = mapped_column(Integer)
    irt_a_vector: Mapped[float] = mapped_column(Float, nullable=False)
    irt_b_vector: Mapped[float] = mapped_column(Float, nullable=False)
    irt_c_parameter: Mapped[float] = mapped_column(Float, nullable=False)
    is_primary_dimension: Mapped[Optional[bool]] = mapped_column(Boolean)
    created_at: Mapped[datetime] = mapped_column(TIMESTAMP(timezone=True), nullable=False)
    updated_at: Mapped[datetime] = mapped_column(TIMESTAMP(timezone=True), nullable=False)
    __table_args__ = (
        UniqueConstraint("item_id", "topic_id", "bloom_dimension_id"),
    )

class LearnerTopicState(Base):
    __tablename__ = "learnertopicstate"
    user_id: Mapped[UUID] = mapped_column(PG_UUID(as_uuid=True), ForeignKey("users.user_id"), primary_key=True)
    topic_id: Mapped[UUID] = mapped_column(PG_UUID(as_uuid=True), ForeignKey("topics.topic_id"), primary_key=True)
    theta_vector: Mapped[Optional[dict]] = mapped_column(JSONB)
    srs_state: Mapped[Optional[dict]] = mapped_column(JSONB)
    mastery_timestamp: Mapped[Optional[datetime]] = mapped_column(TIMESTAMP(timezone=True))
    updated_at: Mapped[datetime] = mapped_column(TIMESTAMP(timezone=True), nullable=False)

class Response(Base):
    __tablename__ = "responses"
    response_id: Mapped[int] = mapped_column(BigInteger, primary_key=True)
    session_id: Mapped[Optional[UUID]] = mapped_column(PG_UUID(as_uuid=True))
    user_id: Mapped[UUID] = mapped_column(PG_UUID(as_uuid=True), ForeignKey("users.user_id"))
    item_id: Mapped[UUID] = mapped_column(PG_UUID(as_uuid=True), ForeignKey("items.item_id"))
    is_correct: Mapped[Optional[bool]] = mapped_column(Boolean)
    response_timestamp: Mapped[datetime] = mapped_column(TIMESTAMP(timezone=True), nullable=False)
    ability_estimate_before: Mapped[Optional[dict]] = mapped_column(JSONB)
    ability_estimate_after: Mapped[Optional[dict]] = mapped_column(JSONB)
    __table_args__ = (
        UniqueConstraint("response_id", "response_timestamp"),
    )
