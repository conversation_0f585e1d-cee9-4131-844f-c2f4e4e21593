CREATE TABLE Responses (
    response_id BIGSERIAL,
    session_id UUID,
    user_id UUID REFERENCES Users(user_id),
    item_id UUID REFERENCES Items(item_id),
    is_correct BOOLEAN,
    response_timestamp TIMESTAMPTZ NOT NULL,
    ability_estimate_before JSONB,
    ability_estimate_after J<PERSON><PERSON><PERSON>,
    PRIMARY KEY (response_id, response_timestamp)
) PARTITION BY RANGE (response_timestamp);

-- Example partition for January 2025
CREATE TABLE Responses_2025_01 PARTITION OF Responses
    FOR VALUES FROM ('2025-01-01 00:00:00+00') TO ('2025-02-01 00:00:00+00');
