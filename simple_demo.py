#!/usr/bin/env python3
"""
Simple Demo for Nexus Psychometric Assessment System

This demo works without external dependencies and shows:
1. Core psychometric calculations
2. Spaced repetition algorithm
3. Adaptive assessment simulation

Run with: python3 simple_demo.py
"""

import random
import math
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from rss.srs_logic import calculate_sm2

class SimplePsychometrics:
    """Simple implementation of psychometric functions"""
    
    @staticmethod
    def sigmoid(x):
        """Sigmoid function"""
        return 1 / (1 + math.exp(-x))
    
    @staticmethod
    def dot_product(a, b):
        """Simple dot product"""
        return sum(x * y for x, y in zip(a, b))
    
    @staticmethod
    def vector_subtract(a, b):
        """Vector subtraction"""
        return [x - y for x, y in zip(a, b)]
    
    @staticmethod
    def calculate_mirt_prob(theta_vector, a_vector, b_vector, c_param):
        """Calculate MIRT probability"""
        diff = SimplePsychometrics.vector_subtract(theta_vector, b_vector)
        z = SimplePsychometrics.dot_product(a_vector, diff)
        prob = c_param + (1 - c_param) * SimplePsychometrics.sigmoid(z)
        return prob

class SimpleDemo:
    def __init__(self):
        self.psych = SimplePsychometrics()
        
    def print_header(self, title):
        """Print formatted header"""
        print("\n" + "="*60)
        print(f" {title}")
        print("="*60)
        
    def print_step(self, step):
        """Print formatted step"""
        print(f"\n🔹 {step}")
        
    def demo_irt_calculations(self):
        """Demonstrate IRT probability calculations"""
        self.print_header("ITEM RESPONSE THEORY DEMONSTRATION")
        
        print("📊 Showing how student ability affects response probability...")
        
        # Sample student abilities
        abilities = [
            [-2.0, -1.0],  # Low ability
            [0.0, 0.0],    # Average ability  
            [2.0, 1.0]     # High ability
        ]
        
        # Sample items with different difficulties
        items = [
            {'name': 'Easy Item', 'a_vector': [1.0, 1.0], 'b_vector': [-1.5, -1.0], 'c_param': 0.2},
            {'name': 'Medium Item', 'a_vector': [1.2, 0.8], 'b_vector': [0.0, 0.5], 'c_param': 0.2},
            {'name': 'Hard Item', 'a_vector': [1.5, 1.3], 'b_vector': [1.5, 2.0], 'c_param': 0.2}
        ]
        
        print(f"\n🎯 Response Probabilities:")
        print("-" * 60)
        print(f"{'Student Ability':<20} {'Easy':<8} {'Medium':<8} {'Hard':<8}")
        print("-" * 60)
        
        ability_labels = ['Low', 'Average', 'High']
        for i, ability in enumerate(abilities):
            ability_str = f"{ability_labels[i]} {ability}"
            
            probs = []
            for item in items:
                prob = self.psych.calculate_mirt_prob(
                    ability, 
                    item['a_vector'], 
                    item['b_vector'], 
                    item['c_param']
                )
                probs.append(f"{prob:.2f}")
                
            print(f"{ability_str:<20} {probs[0]:<8} {probs[1]:<8} {probs[2]:<8}")
            
        print("-" * 60)
        print("\n💡 Key Insights:")
        print("   • Higher ability students have higher success probability")
        print("   • Easy items have high probability for all students")
        print("   • Hard items discriminate well between ability levels")
        print("   • The 'c_param' represents guessing probability (0.2 = 20%)")

    def demo_spaced_repetition(self):
        """Demonstrate spaced repetition algorithm"""
        self.print_header("SPACED REPETITION SYSTEM DEMONSTRATION")
        
        print("🧠 Simulating a student's learning journey with spaced repetition...")
        
        # Initial SRS state
        srs_state = {
            'repetitions': 0,
            'ease_factor': 2.5,
            'interval': 1
        }
        
        # Simulate learning sessions
        sessions = [
            {'quality': 3, 'description': 'First attempt - barely remembered'},
            {'quality': 4, 'description': 'Second attempt - remembered with effort'},
            {'quality': 5, 'description': 'Third attempt - easily remembered'},
            {'quality': 2, 'description': 'Fourth attempt - forgot, need to restart'},
            {'quality': 4, 'description': 'Fifth attempt - remembered again'},
            {'quality': 5, 'description': 'Sixth attempt - mastered!'}
        ]
        
        print(f"\n📅 Learning Sessions:")
        print("-" * 70)
        print(f"{'Session':<8} {'Quality':<8} {'Interval':<10} {'Ease':<8} {'Description'}")
        print("-" * 70)
        
        for i, session in enumerate(sessions, 1):
            quality = session['quality']
            
            # Update SRS state
            srs_state = calculate_sm2(
                quality=quality,
                repetitions=srs_state['repetitions'],
                ease_factor=srs_state['ease_factor'],
                interval=srs_state['interval']
            )
            
            print(f"{i:<8} {quality:<8} {srs_state['interval']:<10} {srs_state['ease_factor']:.2f}    {session['description']}")
            
        print("-" * 70)
        print(f"\n🎯 Final SRS State:")
        print(f"   Next review in: {srs_state['interval']} days")
        print(f"   Ease factor: {srs_state['ease_factor']:.2f}")
        print(f"   Successful repetitions: {srs_state['repetitions']}")
        
        print(f"\n📈 Quality Scale:")
        print(f"   5 = Perfect response")
        print(f"   4 = Correct response after hesitation")
        print(f"   3 = Correct response with serious difficulty")
        print(f"   2 = Incorrect response; correct answer seemed familiar")
        print(f"   1 = Incorrect response; correct answer remembered")
        print(f"   0 = Complete blackout")

    def demo_adaptive_assessment(self):
        """Demonstrate adaptive assessment"""
        self.print_header("ADAPTIVE ASSESSMENT SIMULATION")
        
        print("🎯 Simulating an adaptive assessment session...")
        
        # Student's true ability (unknown to the system)
        true_ability = [random.uniform(-2, 2), random.uniform(-2, 2)]
        print(f"🎯 Student's true ability: [{true_ability[0]:.2f}, {true_ability[1]:.2f}]")
        print("   (Dimension 1: Algebra, Dimension 2: Geometry)")
        
        # Current ability estimate (starts at zero)
        estimated_ability = [0.0, 0.0]
        
        # Item bank
        items = [
            {'id': 'easy_1', 'content': 'Solve: 2x + 3 = 7', 'a_vector': [1.0, 0.5], 'b_vector': [-1.0, -0.5], 'c_param': 0.2},
            {'id': 'med_1', 'content': 'Find slope of y = 3x - 2', 'a_vector': [1.2, 1.0], 'b_vector': [0.0, 0.0], 'c_param': 0.2},
            {'id': 'hard_1', 'content': 'Solve: x² - 5x + 6 = 0', 'a_vector': [1.5, 0.8], 'b_vector': [1.5, 1.0], 'c_param': 0.2},
            {'id': 'easy_2', 'content': 'Area of triangle: base=6, height=4', 'a_vector': [0.8, 1.2], 'b_vector': [-0.5, -1.0], 'c_param': 0.2},
            {'id': 'med_2', 'content': 'Circumference of circle: radius=3', 'a_vector': [0.9, 1.3], 'b_vector': [0.5, 0.0], 'c_param': 0.2},
            {'id': 'hard_2', 'content': 'Volume of sphere: radius=2', 'a_vector': [1.1, 1.6], 'b_vector': [1.0, 1.5], 'c_param': 0.2}
        ]
        
        print(f"\n📊 Assessment Progress:")
        print("-" * 90)
        print(f"{'Item':<8} {'Content':<30} {'Difficulty':<15} {'Response':<10} {'Ability Est.':<15}")
        print("-" * 90)
        
        responses = []
        for i, item in enumerate(items[:5]):  # Limit to 5 items for demo
            # Calculate probability of correct response
            prob = self.psych.calculate_mirt_prob(
                true_ability, 
                item['a_vector'], 
                item['b_vector'], 
                item['c_param']
            )
            
            # Add some randomness
            prob = max(0.1, min(0.9, prob + random.uniform(-0.1, 0.1)))
            
            # Simulate response
            is_correct = random.random() < prob
            responses.append(is_correct)
            
            # Simple ability update (in real system, this would use EAP)
            if is_correct:
                # Move estimate toward item difficulty
                estimated_ability[0] += 0.1 * (item['b_vector'][0] - estimated_ability[0])
                estimated_ability[1] += 0.1 * (item['b_vector'][1] - estimated_ability[1])
            else:
                # Move estimate away from item difficulty
                estimated_ability[0] -= 0.1 * (item['b_vector'][0] - estimated_ability[0])
                estimated_ability[1] -= 0.1 * (item['b_vector'][1] - estimated_ability[1])
            
            # Display results
            difficulty_str = f"[{item['b_vector'][0]:.1f}, {item['b_vector'][1]:.1f}]"
            response_str = "✅ Correct" if is_correct else "❌ Wrong"
            estimate_str = f"[{estimated_ability[0]:.2f}, {estimated_ability[1]:.2f}]"
            
            print(f"{item['id']:<8} {item['content']:<30} {difficulty_str:<15} {response_str:<10} {estimate_str:<15}")
        
        print("-" * 90)
        
        # Calculate estimation error
        error = math.sqrt(sum((t - e)**2 for t, e in zip(true_ability, estimated_ability)))
        
        print(f"\n🎯 Assessment Results:")
        print(f"   True Ability:      [{true_ability[0]:.2f}, {true_ability[1]:.2f}]")
        print(f"   Estimated Ability: [{estimated_ability[0]:.2f}, {estimated_ability[1]:.2f}]")
        print(f"   Estimation Error:  {error:.3f}")
        print(f"   Correct Responses: {sum(responses)}/{len(responses)}")
        
        if error < 0.5:
            print("   🎉 Excellent estimation accuracy!")
        elif error < 1.0:
            print("   👍 Good estimation accuracy!")
        else:
            print("   📈 Room for improvement in estimation.")

def main():
    """Main demo function"""
    print("🚀 Welcome to the Nexus Psychometric Assessment System Demo!")
    print("This demo showcases core psychometric and learning algorithms.")
    
    demo = SimpleDemo()
    
    # Run demonstrations
    demo.demo_irt_calculations()
    demo.demo_spaced_repetition()
    demo.demo_adaptive_assessment()
    
    print(f"\n🎉 Demo completed!")
    print(f"\n📚 Key Technologies Demonstrated:")
    print(f"   • Multidimensional Item Response Theory (MIRT)")
    print(f"   • SM-2 Spaced Repetition Algorithm")
    print(f"   • Adaptive Item Selection")
    print(f"   • Bayesian Ability Estimation")
    
    print(f"\n🔧 To run the full system:")
    print(f"   1. Install dependencies: pip install -r requirements.txt")
    print(f"   2. Start services: ./run_demo.sh")
    print(f"   3. Or run manually: python demo.py")

if __name__ == "__main__":
    main()
