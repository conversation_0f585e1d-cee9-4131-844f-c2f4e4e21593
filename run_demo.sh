#!/bin/bash

# Nexus Assessment System Demo Runner
# This script sets up and runs the complete demo

set -e

echo "🚀 Nexus Assessment System Demo Setup"
echo "======================================"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3 and try again."
    exit 1
fi

echo "📦 Installing Python dependencies..."
pip3 install -r requirements.txt

echo "🐳 Starting services with Docker Compose..."
docker-compose up -d postgres redis

echo "⏳ Waiting for PostgreSQL to be ready..."
sleep 10

# Check if PostgreSQL is ready
until docker-compose exec postgres pg_isready -U postgres > /dev/null 2>&1; do
    echo "   Waiting for PostgreSQL..."
    sleep 2
done

echo "✅ PostgreSQL is ready!"

echo "🗄️  Setting up database with sample data..."
python3 setup_database.py

echo "🚀 Starting Cognitive Assessment Service..."
docker-compose up -d cas

echo "⏳ Waiting for CAS to be ready..."
sleep 5

# Check if CAS is ready
until curl -f http://localhost:8000/health > /dev/null 2>&1; do
    echo "   Waiting for CAS..."
    sleep 2
done

echo "✅ CAS is ready!"

echo "🌐 Starting API Gateway..."
# Create dummy JWT keys for demo
mkdir -p keys
if [ ! -f keys/public.pem ]; then
    openssl genrsa -out keys/private.pem 2048
    openssl rsa -in keys/private.pem -pubout -out keys/public.pem
    echo "🔑 Generated JWT keys for demo"
fi

docker-compose up -d gateway

echo "⏳ Waiting for Gateway to be ready..."
sleep 3

echo ""
echo "🎉 All services are running!"
echo "================================"
echo "📊 Service Status:"
echo "   • PostgreSQL: http://localhost:5432"
echo "   • Redis: http://localhost:6379"
echo "   • Assessment Service: http://localhost:8000"
echo "   • API Gateway: http://localhost:8080"
echo ""
echo "🎯 Running the demo..."
echo "================================"

# Run the demo
python3 demo.py

echo ""
echo "🧹 Cleaning up..."
echo "To stop all services, run: docker-compose down"
echo "To remove all data, run: docker-compose down -v"
