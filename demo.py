#!/usr/bin/env python3
"""
Comprehensive Demo Script for Nexus Psychometric Assessment System

This script demonstrates:
1. Starting an adaptive assessment session
2. Simulating student responses
3. Showing how the system adapts item difficulty
4. Demonstrating spaced repetition logic
5. Displaying final ability estimates

Run with: python demo.py
"""

import asyncio
import json
import random
import time
from typing import Dict, List
import numpy as np
import requests
from datetime import datetime

# Import our modules
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from cas.psychometrics import calculate_mirt_prob
from rss.srs_logic import calculate_sm2

class DemoRunner:
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session_id = None
        
    def print_header(self, title: str):
        """Print a formatted header"""
        print("\n" + "="*60)
        print(f" {title}")
        print("="*60)
        
    def print_step(self, step: str):
        """Print a formatted step"""
        print(f"\n🔹 {step}")
        
    def simulate_student_ability(self) -> np.ndarray:
        """Simulate a student's true ability vector"""
        # Random ability in 2D space (e.g., algebra and geometry)
        return np.array([
            random.uniform(-2, 2),  # Algebra ability
            random.uniform(-2, 2)   # Geometry ability
        ])
        
    def simulate_response(self, item: Dict, true_ability: np.ndarray) -> bool:
        """Simulate a student response based on their true ability"""
        a_vector = np.array(item['a_vector'])
        b_vector = np.array(item['b_vector'])
        c_param = item['c_param']
        
        # Calculate probability of correct response
        prob = calculate_mirt_prob(true_ability, a_vector, b_vector, c_param)
        
        # Add some noise to make it realistic
        prob = max(0.1, min(0.9, prob + random.uniform(-0.1, 0.1)))
        
        # Simulate response
        return random.random() < prob
        
    def start_assessment(self, user_id: str = "demo_user", topic_id: str = "algebra_basics"):
        """Start an assessment session"""
        self.print_step(f"Starting assessment for user: {user_id}, topic: {topic_id}")
        
        try:
            response = requests.post(
                f"{self.base_url}/v1/assessments",
                json={
                    "user_id": user_id,
                    "topic_id": topic_id
                },
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                self.session_id = data['session_id']
                print(f"✅ Session started: {self.session_id}")
                print(f"📝 First item: {data['item']['content']}")
                return data['item']
            else:
                print(f"❌ Failed to start assessment: {response.status_code}")
                print(f"Response: {response.text}")
                return None
                
        except requests.exceptions.ConnectionError:
            print("❌ Could not connect to assessment service. Make sure it's running on port 8000.")
            return None
        except Exception as e:
            print(f"❌ Error starting assessment: {e}")
            return None
            
    def submit_response(self, is_correct: bool):
        """Submit a response to the current item"""
        if not self.session_id:
            print("❌ No active session")
            return None
            
        try:
            response = requests.post(
                f"{self.base_url}/v1/assessments/{self.session_id}/respond",
                json={"is_correct": is_correct},
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ Failed to submit response: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ Error submitting response: {e}")
            return None
            
    def run_assessment_demo(self):
        """Run a complete assessment demonstration"""
        self.print_header("ADAPTIVE ASSESSMENT DEMONSTRATION")
        
        # Simulate student's true ability
        true_ability = self.simulate_student_ability()
        print(f"🎯 Student's true ability: [{true_ability[0]:.2f}, {true_ability[1]:.2f}]")
        print("   (Dimension 1: Algebra, Dimension 2: Geometry)")
        
        # Start assessment
        current_item = self.start_assessment()
        if not current_item:
            return
            
        response_count = 0
        max_responses = 15
        
        print(f"\n📊 Assessment Progress:")
        print("-" * 80)
        print(f"{'Item':<6} {'Difficulty':<20} {'Response':<10} {'Ability Est.':<25} {'Status'}")
        print("-" * 80)
        
        while current_item and response_count < max_responses:
            response_count += 1
            
            # Simulate student response
            is_correct = self.simulate_response(current_item, true_ability)
            
            # Display current item info
            b_vector = current_item['b_vector']
            difficulty_str = f"[{b_vector[0]:.1f}, {b_vector[1]:.1f}]"
            response_str = "✅ Correct" if is_correct else "❌ Wrong"
            
            # Submit response
            result = self.submit_response(is_correct)
            if not result:
                break
                
            # Display results
            if result['status'] == 'completed':
                final_estimate = result['theta_estimate']
                estimate_str = f"[{final_estimate[0]:.2f}, {final_estimate[1]:.2f}]"
                print(f"{response_count:<6} {difficulty_str:<20} {response_str:<10} {estimate_str:<25} COMPLETED")
                break
            else:
                current_estimate = result['theta_estimate']
                estimate_str = f"[{current_estimate[0]:.2f}, {current_estimate[1]:.2f}]"
                print(f"{response_count:<6} {difficulty_str:<20} {response_str:<10} {estimate_str:<25} In Progress")
                current_item = result.get('next_item')
                
        print("-" * 80)
        
        # Final results
        if result and result['status'] == 'completed':
            final_estimate = result['theta_estimate']
            error = np.linalg.norm(np.array(final_estimate) - true_ability)
            
            print(f"\n🎯 Assessment Results:")
            print(f"   True Ability:      [{true_ability[0]:.2f}, {true_ability[1]:.2f}]")
            print(f"   Estimated Ability: [{final_estimate[0]:.2f}, {final_estimate[1]:.2f}]")
            print(f"   Estimation Error:  {error:.3f}")
            print(f"   Items Administered: {response_count}")
            
            if error < 0.5:
                print("   🎉 Excellent estimation accuracy!")
            elif error < 1.0:
                print("   👍 Good estimation accuracy!")
            else:
                print("   📈 Room for improvement in estimation.")
                
    def demo_spaced_repetition(self):
        """Demonstrate spaced repetition algorithm"""
        self.print_header("SPACED REPETITION SYSTEM DEMONSTRATION")
        
        print("🧠 Simulating a student's learning journey with spaced repetition...")
        
        # Initial SRS state
        srs_state = {
            'repetitions': 0,
            'ease_factor': 2.5,
            'interval': 1
        }
        
        # Simulate learning sessions
        sessions = [
            {'quality': 3, 'description': 'First attempt - barely remembered'},
            {'quality': 4, 'description': 'Second attempt - remembered with effort'},
            {'quality': 5, 'description': 'Third attempt - easily remembered'},
            {'quality': 2, 'description': 'Fourth attempt - forgot, need to restart'},
            {'quality': 4, 'description': 'Fifth attempt - remembered again'},
            {'quality': 5, 'description': 'Sixth attempt - mastered!'}
        ]
        
        print(f"\n📅 Learning Sessions:")
        print("-" * 70)
        print(f"{'Session':<8} {'Quality':<8} {'Interval':<10} {'Ease':<8} {'Description'}")
        print("-" * 70)
        
        for i, session in enumerate(sessions, 1):
            quality = session['quality']
            
            # Update SRS state
            srs_state = calculate_sm2(
                quality=quality,
                repetitions=srs_state['repetitions'],
                ease_factor=srs_state['ease_factor'],
                interval=srs_state['interval']
            )
            
            print(f"{i:<8} {quality:<8} {srs_state['interval']:<10} {srs_state['ease_factor']:.2f}    {session['description']}")
            
        print("-" * 70)
        print(f"\n🎯 Final SRS State:")
        print(f"   Next review in: {srs_state['interval']} days")
        print(f"   Ease factor: {srs_state['ease_factor']:.2f}")
        print(f"   Successful repetitions: {srs_state['repetitions']}")
        
    def demo_item_response_theory(self):
        """Demonstrate IRT probability calculations"""
        self.print_header("ITEM RESPONSE THEORY DEMONSTRATION")
        
        print("📊 Showing how item difficulty affects response probability...")
        
        # Sample student abilities
        abilities = [
            np.array([-2.0, -1.0]),  # Low ability
            np.array([0.0, 0.0]),    # Average ability  
            np.array([2.0, 1.0])     # High ability
        ]
        
        # Sample items with different difficulties
        items = [
            {'name': 'Easy Item', 'a_vector': [1.0, 1.0], 'b_vector': [-1.5, -1.0], 'c_param': 0.2},
            {'name': 'Medium Item', 'a_vector': [1.2, 0.8], 'b_vector': [0.0, 0.5], 'c_param': 0.2},
            {'name': 'Hard Item', 'a_vector': [1.5, 1.3], 'b_vector': [1.5, 2.0], 'c_param': 0.2}
        ]
        
        print(f"\n🎯 Response Probabilities:")
        print("-" * 60)
        print(f"{'Student Ability':<20} {'Easy':<8} {'Medium':<8} {'Hard':<8}")
        print("-" * 60)
        
        for i, ability in enumerate(abilities):
            ability_labels = ['Low', 'Average', 'High']
            ability_str = f"{ability_labels[i]} {ability}"
            
            probs = []
            for item in items:
                prob = calculate_mirt_prob(
                    ability, 
                    np.array(item['a_vector']), 
                    np.array(item['b_vector']), 
                    item['c_param']
                )
                probs.append(f"{prob:.2f}")
                
            print(f"{ability_str:<20} {probs[0]:<8} {probs[1]:<8} {probs[2]:<8}")
            
        print("-" * 60)
        print("\n💡 Notice how:")
        print("   • Higher ability students have higher success probability")
        print("   • Easy items have high probability for all students")
        print("   • Hard items discriminate well between ability levels")

def main():
    """Main demo function"""
    print("🚀 Welcome to the Nexus Psychometric Assessment System Demo!")
    print("This demo will showcase the adaptive testing and spaced repetition features.")
    
    demo = DemoRunner()
    
    # Run different demonstrations
    demo.demo_item_response_theory()
    demo.demo_spaced_repetition()
    
    print(f"\n🔧 Starting FastAPI server demo...")
    print("Note: Make sure to run 'uvicorn cas.main:app --reload --port 8000' in another terminal")
    
    input("\nPress Enter when the server is running to continue with the assessment demo...")
    
    demo.run_assessment_demo()
    
    print(f"\n🎉 Demo completed! Thank you for exploring the Nexus Assessment System.")
    print(f"📚 Key features demonstrated:")
    print(f"   • Multidimensional Item Response Theory (MIRT)")
    print(f"   • Adaptive item selection using Fisher Information")
    print(f"   • Expected A Posteriori (EAP) ability estimation")
    print(f"   • SM-2 Spaced Repetition Algorithm")

if __name__ == "__main__":
    main()
