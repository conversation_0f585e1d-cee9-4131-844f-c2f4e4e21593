#!/usr/bin/env python3
"""
Database Setup Script for Nexus Psychometric Assessment System

This script:
1. Creates the database tables
2. Inserts sample data for testing
3. Sets up topics, items, and user data

Run with: python setup_database.py
"""

import asyncio
import uuid
import json
from datetime import datetime, timezone
import numpy as np
from sqlalchemy.ext.asyncio import create_async_engine, async_sessionmaker
from sqlalchemy import text
import bcrypt

# Import our models
from shared.models import Base, User, Item, Topic, CognitiveQMatrix, LearnerTopicState

DATABASE_URL = "postgresql+asyncpg://postgres:password@localhost:5432/nexus_db"

async def create_database():
    """Create the database if it doesn't exist"""
    # Connect to postgres database to create nexus_db
    engine = create_async_engine("postgresql+asyncpg://postgres:password@localhost:5432/postgres")
    
    async with engine.begin() as conn:
        # Check if database exists
        result = await conn.execute(
            text("SELECT 1 FROM pg_database WHERE datname = 'nexus_db'")
        )
        if not result.fetchone():
            await conn.execute(text("CREATE DATABASE nexus_db"))
            print("✅ Created database: nexus_db")
        else:
            print("📋 Database nexus_db already exists")
    
    await engine.dispose()

async def setup_tables():
    """Create all tables"""
    engine = create_async_engine(DATABASE_URL, echo=True)
    
    async with engine.begin() as conn:
        # Enable pgcrypto extension
        await conn.execute(text("CREATE EXTENSION IF NOT EXISTS pgcrypto"))
        await conn.execute(text("CREATE EXTENSION IF NOT EXISTS ltree"))
        
        # Create all tables
        await conn.run_sync(Base.metadata.drop_all)
        await conn.run_sync(Base.metadata.create_all)
        print("✅ Created all database tables")
    
    return engine

async def insert_sample_data(engine):
    """Insert sample data for testing"""
    async_session = async_sessionmaker(engine, expire_on_commit=False)
    
    async with async_session() as session:
        # Create sample users
        users_data = [
            {"email": "<EMAIL>", "password": "password123"},
            {"email": "<EMAIL>", "password": "password123"},
            {"email": "<EMAIL>", "password": "password123"},
        ]
        
        users = []
        for user_data in users_data:
            hashed_password = bcrypt.hashpw(
                user_data["password"].encode('utf-8'), 
                bcrypt.gensalt()
            ).decode('utf-8')
            
            user = User(
                user_id=uuid.uuid4(),
                email=user_data["email"],
                hashed_password=hashed_password,
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            )
            users.append(user)
            session.add(user)
        
        await session.commit()
        print(f"✅ Created {len(users)} sample users")
        
        # Create sample topics
        topics_data = [
            {"name": "Mathematics", "slug": "mathematics", "parent": None},
            {"name": "Algebra", "slug": "algebra", "parent": "mathematics"},
            {"name": "Geometry", "slug": "geometry", "parent": "mathematics"},
            {"name": "Linear Equations", "slug": "linear-equations", "parent": "algebra"},
            {"name": "Quadratic Equations", "slug": "quadratic-equations", "parent": "algebra"},
        ]
        
        topics = {}
        for topic_data in topics_data:
            topic_id = uuid.uuid4()
            parent_id = None
            path = topic_data["slug"]
            
            if topic_data["parent"]:
                parent_topic = topics[topic_data["parent"]]
                parent_id = parent_topic.topic_id
                path = f"{parent_topic.path}.{topic_data['slug']}"
            
            topic = Topic(
                topic_id=topic_id,
                topic_name=topic_data["name"],
                slug=topic_data["slug"],
                parent_topic_id=parent_id,
                path=path,
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            )
            topics[topic_data["slug"]] = topic
            session.add(topic)
        
        await session.commit()
        print(f"✅ Created {len(topics)} sample topics")
        
        # Create sample items
        algebra_topic = topics["algebra"]
        geometry_topic = topics["geometry"]
        
        items_data = [
            {
                "type": "multiple_choice",
                "content": {
                    "question": "Solve for x: 2x + 5 = 13",
                    "options": ["x = 4", "x = 6", "x = 8", "x = 9"],
                    "correct_answer": 0
                },
                "topic": algebra_topic,
                "difficulty": [-0.5, 0.2]  # Easy algebra, medium geometry component
            },
            {
                "type": "multiple_choice", 
                "content": {
                    "question": "What is the slope of the line y = 3x - 2?",
                    "options": ["2", "3", "-2", "-3"],
                    "correct_answer": 1
                },
                "topic": algebra_topic,
                "difficulty": [0.0, -0.5]  # Medium algebra, easy geometry
            },
            {
                "type": "multiple_choice",
                "content": {
                    "question": "Find the area of a triangle with base 8 and height 6",
                    "options": ["24", "48", "14", "28"],
                    "correct_answer": 0
                },
                "topic": geometry_topic,
                "difficulty": [-1.0, 0.0]  # Easy algebra, medium geometry
            },
            {
                "type": "multiple_choice",
                "content": {
                    "question": "Solve the quadratic equation: x² - 5x + 6 = 0",
                    "options": ["x = 2, 3", "x = 1, 6", "x = -2, -3", "x = 0, 5"],
                    "correct_answer": 0
                },
                "topic": algebra_topic,
                "difficulty": [1.5, 0.5]  # Hard algebra, medium geometry
            },
            {
                "type": "multiple_choice",
                "content": {
                    "question": "What is the circumference of a circle with radius 5?",
                    "options": ["10π", "25π", "5π", "15π"],
                    "correct_answer": 0
                },
                "topic": geometry_topic,
                "difficulty": [0.5, 1.0]  # Medium algebra, hard geometry
            }
        ]
        
        items = []
        for item_data in items_data:
            item_id = uuid.uuid4()
            item = Item(
                item_id=item_id,
                item_type=item_data["type"],
                content=item_data["content"],
                author_id=users[0].user_id,
                status="active",
                version=1,
                created_at=datetime.now(timezone.utc),
                updated_at=datetime.now(timezone.utc)
            )
            items.append(item)
            session.add(item)
            
            # Create cognitive Q-matrix entries
            # Dimension 1: Algebra, Dimension 2: Geometry
            for dim_id, (a_val, b_val) in enumerate(zip([1.2, 0.8], item_data["difficulty"])):
                q_matrix = CognitiveQMatrix(
                    cognitive_q_matrix_id=uuid.uuid4(),
                    item_id=item_id,
                    topic_id=item_data["topic"].topic_id,
                    bloom_dimension_id=dim_id + 1,
                    irt_a_vector=a_val,
                    irt_b_vector=b_val,
                    irt_c_parameter=0.2,
                    is_primary_dimension=(dim_id == 0),
                    created_at=datetime.now(timezone.utc),
                    updated_at=datetime.now(timezone.utc)
                )
                session.add(q_matrix)
        
        await session.commit()
        print(f"✅ Created {len(items)} sample items with Q-matrix entries")
        
        # Create sample learner states
        for user in users[:2]:  # Only for first 2 users
            for topic_slug in ["algebra", "geometry"]:
                topic = topics[topic_slug]
                
                # Random initial ability
                theta_vector = {
                    "algebra": np.random.normal(0, 1),
                    "geometry": np.random.normal(0, 1)
                }
                
                srs_state = {
                    "repetitions": 0,
                    "ease_factor": 2.5,
                    "interval": 1,
                    "last_review": None
                }
                
                learner_state = LearnerTopicState(
                    user_id=user.user_id,
                    topic_id=topic.topic_id,
                    theta_vector=theta_vector,
                    srs_state=srs_state,
                    mastery_timestamp=None,
                    updated_at=datetime.now(timezone.utc)
                )
                session.add(learner_state)
        
        await session.commit()
        print("✅ Created sample learner states")

async def main():
    """Main setup function"""
    print("🚀 Setting up Nexus Assessment System Database...")
    print("=" * 60)
    
    try:
        # Create database
        await create_database()
        
        # Setup tables and insert data
        engine = await setup_tables()
        await insert_sample_data(engine)
        
        await engine.dispose()
        
        print("\n" + "=" * 60)
        print("🎉 Database setup completed successfully!")
        print("\n📋 What was created:")
        print("   • Database: nexus_db")
        print("   • Tables: users, items, topics, cognitiveqmatrix, learnertopicstate, responses")
        print("   • Sample users: <EMAIL>, <EMAIL>, <EMAIL>")
        print("   • Sample topics: Mathematics hierarchy (Algebra, Geometry, etc.)")
        print("   • Sample items: 5 test questions with IRT parameters")
        print("   • Sample learner states: Initial ability estimates")
        
        print("\n🔧 Next steps:")
        print("   1. Start the FastAPI server: uvicorn cas.main:app --reload --port 8000")
        print("   2. Run the demo: python demo.py")
        
    except Exception as e:
        print(f"❌ Error during setup: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
