def calculate_sm2(quality: int, repetitions: int, ease_factor: float, interval: int) -> dict:
    """
    SM-2 spaced repetition algorithm.
    Args:
        quality (int): Response quality (0-5).
        repetitions (int): Number of successful repetitions.
        ease_factor (float): Current ease factor.
        interval (int): Current interval (days).
    Returns:
        dict: Updated state with keys 'repetitions', 'ease_factor', 'interval'.
    """
    if quality >= 3:
        new_repetitions = repetitions + 1
        new_ease_factor = ease_factor + (0.1 - (5 - quality) * (0.08 + (5 - quality) * 0.02))
        if new_ease_factor < 1.3:
            new_ease_factor = 1.3
        if new_repetitions == 1:
            new_interval = 1
        elif new_repetitions == 2:
            new_interval = 6
        else:
            new_interval = int(round(interval * new_ease_factor))
    else:
        new_repetitions = 1
        new_interval = 1
        new_ease_factor = ease_factor
    return {
        'repetitions': new_repetitions,
        'ease_factor': new_ease_factor,
        'interval': new_interval
    }
