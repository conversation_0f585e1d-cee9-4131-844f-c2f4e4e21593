import uuid
from fastapi import FastAP<PERSON>, HTTPException
from pydantic import BaseModel
from typing import Dict, List, Optional
import numpy as np

from cas.psychometrics import update_theta_eap, select_next_item

app = FastAPI()

# In-memory session state
session_store: Dict[str, dict] = {}

# --- Schemas ---
class StartAssessmentRequest(BaseModel):
    user_id: str
    topic_id: str

class StartAssessmentResponse(BaseModel):
    session_id: str
    item: dict

class RespondRequest(BaseModel):
    is_correct: bool

class RespondResponse(BaseModel):
    status: str
    next_item: Optional[dict] = None
    theta_estimate: Optional[List[float]] = None

# --- Dummy Item Bank Service ---
def get_warmup_items(topic_id: str) -> List[dict]:
    # Simulate 5 items with varied difficulties
    items = []
    for i in range(5):
        items.append({
            "item_id": f"warmup_{i}",
            "a_vector": [1.0, 1.0],
            "b_vector": [i - 2, i - 2],
            "c_param": 0.2,
            "content": f"Warmup item {i+1}"
        })
    return items

def get_candidate_items(topic_id: str) -> List[dict]:
    # Simulate 10 candidate items
    items = []
    for i in range(10):
        items.append({
            "item_id": f"cand_{i}",
            "a_vector": [1.0, 1.0],
            "b_vector": [np.random.uniform(-2, 2), np.random.uniform(-2, 2)],
            "c_param": 0.2,
            "content": f"Candidate item {i+1}"
        })
    return items

# --- Utility ---
def standard_error(theta_estimate: np.ndarray) -> float:
    # Dummy standard error: use norm of theta_estimate for illustration
    return float(np.linalg.norm(theta_estimate)) / 10

# --- Endpoints ---

@app.post("/v1/assessments", response_model=StartAssessmentResponse)
def start_assessment(req: StartAssessmentRequest):
    session_id = str(uuid.uuid4())
    current_theta = np.zeros(2)
    response_history = []
    warmup_items = get_warmup_items(req.topic_id)
    session_store[session_id] = {
        "user_id": req.user_id,
        "topic_id": req.topic_id,
        "current_theta": current_theta,
        "response_history": response_history,
        "items_queue": warmup_items.copy(),
        "max_items": 20,
        "threshold": 0.3
    }
    first_item = warmup_items[0]
    return StartAssessmentResponse(session_id=session_id, item=first_item)

@app.post("/v1/assessments/{session_id}/respond", response_model=RespondResponse)
def respond_to_item(session_id: str, req: RespondRequest):
    session = session_store.get(session_id)
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    # Pop the current item
    if not session["items_queue"]:
        raise HTTPException(status_code=400, detail="No items left in queue")
    current_item = session["items_queue"].pop(0)
    # Add response to history
    session["response_history"].append((
        np.array(current_item["a_vector"]),
        np.array(current_item["b_vector"]),
        current_item["c_param"],
        int(req.is_correct)
    ))
    # Update theta
    new_theta = update_theta_eap(session["current_theta"], session["response_history"])
    session["current_theta"] = new_theta
    # Check termination
    if len(session["response_history"]) >= session["max_items"] or standard_error(new_theta) < session["threshold"]:
        # Persist theta to Learner Profile Service (stub)
        # Publish ConceptMastered event to Kafka (stub)
        # Remove session from store
        del session_store[session_id]
        return RespondResponse(
            status="completed",
            theta_estimate=new_theta.tolist()
        )
    # If warmup items exhausted, get new candidates
    if not session["items_queue"]:
        candidate_items = get_candidate_items(session["topic_id"])
        next_item = select_next_item(session["current_theta"], candidate_items)
        session["items_queue"] = [next_item]
    next_item = session["items_queue"][0]
    return RespondResponse(
        status="in_progress",
        next_item=next_item,
        theta_estimate=new_theta.tolist()
    )
