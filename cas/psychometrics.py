import numpy as np
from scipy.special import expit
from scipy.stats import multivariate_normal
from scipy.integrate import nquad

def calculate_mirt_prob(theta_vector, a_vector, b_vector, c_param):
    """
    Compensatory MIRT probability of correct response.
    Args:
        theta_vector (np.ndarray): Learner ability vector.
        a_vector (np.ndarray): Item discrimination vector.
        b_vector (np.ndarray): Item difficulty vector.
        c_param (float): Guessing parameter.
    Returns:
        float: Probability of correct response.
    """
    z = np.dot(a_vector, theta_vector - b_vector)
    prob = c_param + (1 - c_param) * expit(z)
    return prob

def update_theta_eap(current_theta, response_history):
    """
    EAP ability estimation for MIRT.
    Args:
        current_theta (np.ndarray): Current ability estimate.
        response_history (list): List of tuples (a_vector, b_vector, c_param, response).
    Returns:
        np.ndarray: Updated theta vector.
    """
    dim = len(current_theta)
    prior = multivariate_normal(mean=np.zeros(dim), cov=np.eye(dim))

    def likelihood(theta):
        prob = 1.0
        for a, b, c, resp in response_history:
            p = calculate_mirt_prob(theta, a, b, c)
            prob *= p if resp == 1 else (1 - p)
        return prob

    def posterior(theta):
        return likelihood(theta) * prior.pdf(theta)

    # Numerical integration over theta space
    bounds = [(-4, 4)] * dim
    norm_const, _ = nquad(posterior, bounds)
    def eap_component(*theta):
        theta = np.array(theta)
        return theta * posterior(theta)
    eap_vec = []
    for i in range(dim):
        def eap_i(*theta):
            return theta[i] * posterior(theta)
        num, _ = nquad(eap_i, bounds)
        eap_vec.append(num / norm_const)
    return np.array(eap_vec)

def select_next_item(theta_estimate, candidate_items):
    """
    Select next item by Maximum Fisher Information (determinant).
    Args:
        theta_estimate (np.ndarray): Current ability estimate.
        candidate_items (list): List of dicts with keys 'a_vector', 'b_vector', 'c_param'.
    Returns:
        dict: Selected item.
    """
    def fisher_information(a, b, c, theta):
        p = calculate_mirt_prob(theta, a, b, c)
        q = 1 - p
        # Gradient of logit argument w.r.t theta is a
        info = np.outer(a, a) * (1 - c) * p * q / ((1 - c) ** 2)
        return info

    max_det = -np.inf
    selected_item = None
    for item in candidate_items:
        a = np.array(item['a_vector'])
        b = np.array(item['b_vector'])
        c = item['c_param']
        info_matrix = fisher_information(a, b, c, theta_estimate)
        det = np.linalg.det(info_matrix)
        if det > max_det:
            max_det = det
            selected_item = item
    return selected_item
