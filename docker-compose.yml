version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15
    container_name: nexus-postgres
    environment:
      POSTGRES_DB: nexus_db
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./nexus_core_tables.sql:/docker-entrypoint-initdb.d/01-core-tables.sql
      - ./topics.sql:/docker-entrypoint-initdb.d/02-topics.sql
      - ./cognitive_learner_tables.sql:/docker-entrypoint-initdb.d/03-cognitive-learner.sql
      - ./responses.sql:/docker-entrypoint-initdb.d/04-responses.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Cognitive Assessment Service (FastAPI)
  cas:
    build:
      context: .
      dockerfile: Dockerfile.cas
    container_name: nexus-cas
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=postgresql+asyncpg://postgres:password@postgres:5432/nexus_db
    depends_on:
      postgres:
        condition: service_healthy
    volumes:
      - ./cas:/app/cas
      - ./shared:/app/shared
    command: uvicorn cas.main:app --host 0.0.0.0 --port 8000 --reload

  # API Gateway (Go)
  gateway:
    build:
      context: ./gateway
      dockerfile: Dockerfile
    container_name: nexus-gateway
    ports:
      - "8080:8080"
    environment:
      - JWT_PUBLIC_KEY_PATH=/app/keys/public.pem
    depends_on:
      - cas
    volumes:
      - ./keys:/app/keys:ro

  # Redis for session storage (optional)
  redis:
    image: redis:7-alpine
    container_name: nexus-redis
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
