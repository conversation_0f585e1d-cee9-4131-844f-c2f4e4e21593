package main

import (
	"log"
	"net/http"
	"net/http/httputil"
	"net/url"
	"strings"
)

func main() {
	mux := http.NewServeMux()

	// Routing map
	routes := map[string]string{
		"/api/v1/items/":       "http://item-bank-service:8000",
		"/api/v1/profiles/":    "http://learner-profile-service:8000",
		"/api/v1/assessments/": "http://cognitive-assessment-service:8000",
	}

	// Register reverse proxies for each route
	for prefix, target := range routes {
		targetURL, err := url.Parse(target)
		if err != nil {
			log.Fatalf("Invalid target URL: %s", target)
		}
		proxy := httputil.NewSingleHostReverseProxy(targetURL)
		handler := http.HandlerFunc(func(pfx string, prx *httputil.ReverseProxy) http.HandlerFunc {
			return func(w http.ResponseWriter, r *http.Request) {
				// Rewrite the request URL path to remove the prefix if needed
				r.URL.Path = strings.TrimPrefix(r.URL.Path, pfx)
				prx.ServeHTTP(w, r)
			}
		}(prefix, proxy))
		// Apply JWT middleware to /api/v1/ routes
		if strings.HasPrefix(prefix, "/api/v1/") {
			mux.Handle(prefix, AuthMiddleware(handler))
		} else {
			mux.Handle(prefix, handler)
		}
	}

	// Fallback handler
	mux.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
		http.NotFound(w, r)
	})

	log.Println("API Gateway listening on :8080")
	if err := http.ListenAndServe(":8080", mux); err != nil {
		log.Fatalf("Server failed: %v", err)
	}
}
