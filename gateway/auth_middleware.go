package main

import (
	"context"
	"crypto/rsa"
	"encoding/pem"
	"errors"
	"io/ioutil"
	"net/http"
	"os"
	"strings"

	"github.com/golang-jwt/jwt/v5"
)

type contextKey string

const (
	userIDKey contextKey = "user_id"
	emailKey  contextKey = "email"
)

func loadPublicKey() (*rsa.PublicKey, error) {
	pubKeyPath := os.Getenv("JWT_PUBLIC_KEY_PATH")
	if pubKeyPath == "" {
		return nil, errors.New("JWT_PUBLIC_KEY_PATH not set")
	}
	data, err := ioutil.ReadFile(pubKeyPath)
	if err != nil {
		return nil, err
	}
	block, _ := pem.Decode(data)
	if block == nil {
		return nil, errors.New("failed to parse PEM block")
	}
	pubKey, err := jwt.ParseRSAPublicKeyFromPEM(data)
	if err != nil {
		return nil, err
	}
	return pubKey, nil
}

func AuthMiddleware(next http.Handler) http.Handler {
	pubKey, err := loadPublicKey()
	if err != nil {
		panic("JWT public key error: " + err.Error())
	}
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		authHeader := r.Header.Get("Authorization")
		if !strings.HasPrefix(authHeader, "Bearer ") {
			http.Error(w, "Unauthorized: missing token", http.StatusUnauthorized)
			return
		}
		tokenStr := strings.TrimPrefix(authHeader, "Bearer ")

		token, err := jwt.Parse(tokenStr, func(token *jwt.Token) (interface{}, error) {
			if _, ok := token.Method.(*jwt.SigningMethodRSA); !ok {
				return nil, errors.New("unexpected signing method")
			}
			return pubKey, nil
		}, jwt.WithValidMethods([]string{"RS256"}))
		if err != nil || !token.Valid {
			http.Error(w, "Unauthorized: invalid token", http.StatusUnauthorized)
			return
		}

		claims, ok := token.Claims.(jwt.MapClaims)
		if !ok {
			http.Error(w, "Unauthorized: invalid claims", http.StatusUnauthorized)
			return
		}
		// Validate exp and iss
		if !claims.VerifyExpiresAt(jwt.TimeFunc().Unix(), true) {
			http.Error(w, "Unauthorized: token expired", http.StatusUnauthorized)
			return
		}
		if !claims.VerifyIssuer("nexus-gateway", true) {
			http.Error(w, "Unauthorized: invalid issuer", http.StatusUnauthorized)
			return
		}
		// Extract user claims
		userID, _ := claims["user_id"].(string)
		email, _ := claims["email"].(string)
		ctx := context.WithValue(r.Context(), userIDKey, userID)
		ctx = context.WithValue(ctx, emailKey, email)
		next.ServeHTTP(w, r.WithContext(ctx))
	})
}
