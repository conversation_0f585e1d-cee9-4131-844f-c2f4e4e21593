# Nexus Psychometric Assessment System

A comprehensive adaptive testing and spaced repetition system built with FastAPI, Go, and PostgreSQL.

## 🚀 Quick Start

### Option 1: Automated Demo (Recommended)
```bash
./run_demo.sh
```

This script will:
- Install Python dependencies
- Start all services with Docker
- Set up the database with sample data
- Run the interactive demo

### Option 2: Manual Setup

1. **Install Dependencies**
```bash
pip install -r requirements.txt
```

2. **Start Database**
```bash
docker-compose up -d postgres redis
```

3. **Setup Database**
```bash
python setup_database.py
```

4. **Start Services**
```bash
# Start Assessment Service
uvicorn cas.main:app --reload --port 8000

# Start Gateway (in another terminal)
cd gateway && go run *.go
```

5. **Run Demo**
```bash
python demo.py
```

## 🏗️ Architecture

### Services
- **Cognitive Assessment Service (CAS)**: FastAPI service for adaptive testing
- **API Gateway**: Go-based reverse proxy with JWT authentication
- **Database**: PostgreSQL with sample data
- **Cache**: Redis for session storage

### Key Features
- **Multidimensional Item Response Theory (MIRT)**: Advanced psychometric modeling
- **Adaptive Item Selection**: Uses Fisher Information for optimal item selection
- **Expected A Posteriori (EAP)**: Bayesian ability estimation
- **SM-2 Spaced Repetition**: Optimized review scheduling
- **JWT Authentication**: Secure API access

## 📊 Demo Features

The demo showcases:

1. **Item Response Theory Calculations**
   - Shows how student ability affects response probability
   - Demonstrates item difficulty and discrimination

2. **Spaced Repetition Algorithm**
   - Simulates learning sessions with quality ratings
   - Shows how review intervals adapt based on performance

3. **Adaptive Assessment**
   - Real-time ability estimation
   - Dynamic item selection based on current ability
   - Termination criteria based on measurement precision

## 🗄️ Database Schema

### Core Tables
- `users`: User accounts and authentication
- `items`: Assessment items with content and metadata
- `topics`: Hierarchical topic structure
- `cognitiveqmatrix`: IRT parameters for items
- `learnertopicstate`: Student ability estimates and SRS state
- `responses`: Student response history

### Sample Data
- 3 demo users (<EMAIL>, <EMAIL>, <EMAIL>)
- Mathematics topic hierarchy (Algebra, Geometry, etc.)
- 5 sample assessment items with IRT parameters
- Initial learner states with random abilities

## 🔧 API Endpoints

### Assessment Service (Port 8000)
- `POST /v1/assessments` - Start new assessment session
- `POST /v1/assessments/{session_id}/respond` - Submit response
- `GET /health` - Health check

### Gateway (Port 8080)
- Routes requests to appropriate services
- Handles JWT authentication
- Provides unified API access

## 🧪 Testing

Run the demo to see:
- Adaptive item selection in action
- Real-time ability estimation
- Spaced repetition scheduling
- IRT probability calculations

## 📈 Psychometric Models

### MIRT (Multidimensional IRT)
```
P(X = 1|θ) = c + (1-c) * sigmoid(a'(θ - b))
```
Where:
- θ: Ability vector (e.g., [algebra_ability, geometry_ability])
- a: Discrimination vector
- b: Difficulty vector  
- c: Guessing parameter

### SM-2 Spaced Repetition
- Quality ratings (0-5) determine next review interval
- Ease factor adjusts based on performance
- Failed items reset to 1-day interval

## 🐳 Docker Services

```yaml
services:
  postgres:    # Database (port 5432)
  redis:       # Cache (port 6379)
  cas:         # Assessment Service (port 8000)
  gateway:     # API Gateway (port 8080)
```

## 🔑 Environment Variables

- `DATABASE_URL`: PostgreSQL connection string
- `JWT_PUBLIC_KEY_PATH`: Path to JWT public key
- `JWT_PRIVATE_KEY_PATH`: Path to JWT private key

## 📝 Next Steps

1. **Add More Items**: Expand the item bank with diverse content
2. **Implement Authentication**: Add user registration and login
3. **Add Analytics**: Track learning progress and outcomes
4. **Mobile Support**: Create mobile-friendly interfaces
5. **Machine Learning**: Add automated item generation

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is for educational and demonstration purposes.

---

**Happy Learning! 🎓**
