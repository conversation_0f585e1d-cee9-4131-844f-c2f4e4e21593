#!/usr/bin/env python3
"""
Quick verification script to test if the demo is working correctly
"""

import requests
import json
import time

def test_health():
    """Test health endpoint"""
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ Health check passed")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check failed: {e}")
        return False

def test_assessment():
    """Test assessment endpoints"""
    try:
        # Start assessment
        start_response = requests.post(
            "http://localhost:8000/v1/assessments",
            json={"user_id": "test_user", "topic_id": "algebra_basics"},
            timeout=10
        )
        
        if start_response.status_code != 200:
            print(f"❌ Start assessment failed: {start_response.status_code}")
            return False
            
        data = start_response.json()
        session_id = data['session_id']
        print(f"✅ Assessment started: {session_id}")
        
        # Submit response
        response_data = requests.post(
            f"http://localhost:8000/v1/assessments/{session_id}/respond",
            json={"is_correct": True},
            timeout=15
        )
        
        if response_data.status_code != 200:
            print(f"❌ Submit response failed: {response_data.status_code}")
            return False
            
        result = response_data.json()
        print(f"✅ Response submitted successfully")
        print(f"   Status: {result['status']}")
        if 'theta_estimate' in result:
            theta = result['theta_estimate']
            print(f"   Ability estimate: [{theta[0]:.3f}, {theta[1]:.3f}]")
        
        return True
        
    except Exception as e:
        print(f"❌ Assessment test failed: {e}")
        return False

def main():
    print("🧪 Verifying Nexus Assessment System Demo")
    print("=" * 50)
    
    print("🔍 Testing health endpoint...")
    health_ok = test_health()
    
    if health_ok:
        print("\n🎯 Testing assessment functionality...")
        assessment_ok = test_assessment()
        
        if assessment_ok:
            print("\n🎉 All tests passed! The demo is working correctly.")
            print("\n🚀 You can now run:")
            print("   • python3 simple_demo.py (standalone demo)")
            print("   • source venv/bin/activate && python demo.py (full demo)")
            print("   • ./run_demo.sh (complete Docker setup)")
        else:
            print("\n❌ Assessment tests failed. Check the logs with:")
            print("   docker-compose logs cas")
    else:
        print("\n❌ Health check failed. Make sure services are running:")
        print("   docker-compose up -d")

if __name__ == "__main__":
    main()
