CREATE TABLE CognitiveQMatrix (
    cognitive_q_matrix_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    item_id UUID REFERENCES Items(item_id),
    topic_id UUID REFERENCES Topics(topic_id),
    bloom_dimension_id INT,
    irt_a_vector FLOAT NOT NULL,
    irt_b_vector FLOAT NOT NULL,
    irt_c_parameter FLOAT NOT NULL,
    is_primary_dimension BOOLEAN,
    created_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    UNIQUE (item_id, topic_id, bloom_dimension_id)
);

CREATE TABLE LearnerTopicState (
    user_id UUID REFERENCES Users(user_id),
    topic_id UUID REFERENCES Topics(topic_id),
    theta_vector JSONB,
    srs_state JSONB,
    mastery_timestamp TIMESTAMPTZ,
    updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
    PRIMARY KEY (user_id, topic_id)
);

CREATE INDEX learner_topic_state_srs_state_gin_idx ON LearnerTopicState USING GIN (srs_state);
